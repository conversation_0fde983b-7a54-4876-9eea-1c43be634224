/* CSS Custom Properties */
:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #f8fafc;
    --accent-color: #10b981;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --border-color: #e5e7eb;
    --white: #ffffff;
    --gradient: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --transition: all 0.3s ease;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--white);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: 3rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }

p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 500;
    text-align: center;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary {
    background: var(--gradient);
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--white);
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: var(--white);
}

.btn-large {
    padding: 16px 32px;
    font-size: 1.1rem;
}

/* Header */
.header {
    background: var(--white);
    box-shadow: var(--shadow-sm);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.nav {
    padding: 1rem 0;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo h2 {
    color: var(--primary-color);
    margin: 0;
    font-size: 1.5rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    transition: var(--transition);
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    margin: 3px 0;
    transition: var(--transition);
}

/* Hero Section */
.hero {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.hero .container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 1.5rem;
}

.highlight {
    color: var(--primary-color);
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    color: var(--text-secondary);
}

.hero-features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
}

.feature {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.feature i {
    color: var(--accent-color);
    font-size: 1.2rem;
}

.hero-cta {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Hero Mockup */
.mockup-container {
    position: relative;
    height: 400px;
}

.mockup-phone, .mockup-laptop {
    position: absolute;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
}

.mockup-phone {
    width: 180px;
    height: 320px;
    background: #1f2937;
    right: 0;
    top: 0;
    padding: 20px 15px;
}

.mockup-laptop {
    width: 300px;
    height: 200px;
    background: #374151;
    left: 0;
    bottom: 0;
    padding: 15px;
}

.mockup-screen {
    width: 100%;
    height: 100%;
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 10px;
}

.mockup-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.mockup-header {
    height: 20px;
    background: var(--primary-color);
    border-radius: 4px;
}

.mockup-text {
    height: 12px;
    background: var(--border-color);
    border-radius: 4px;
}

.mockup-text.short {
    width: 60%;
}

.mockup-button {
    height: 25px;
    background: var(--accent-color);
    border-radius: 4px;
    margin-top: auto;
}

.mockup-nav {
    height: 15px;
    background: var(--border-color);
    border-radius: 4px;
    margin-bottom: 10px;
}

.mockup-hero {
    height: 40px;
    background: var(--primary-color);
    border-radius: 4px;
    margin-bottom: 10px;
}

.mockup-sections {
    display: flex;
    gap: 5px;
    flex: 1;
}

.mockup-section {
    flex: 1;
    background: var(--border-color);
    border-radius: 4px;
}

/* Section Styles */
section {
    padding: 80px 0;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.2rem;
    color: var(--text-secondary);
}

/* Services Section */
.services {
    background: var(--white);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.service-card {
    background: var(--white);
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: var(--transition);
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.service-icon i {
    font-size: 2rem;
    color: var(--white);
}

.service-card h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.service-card p {
    color: var(--text-secondary);
}

/* Examples Section */
.examples {
    background: var(--secondary-color);
}

.examples-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    align-items: stretch;
}

@media (min-width: 768px) {
    .examples-grid {
        grid-template-rows: repeat(auto-fit, 1fr);
    }
}

.example-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    height: 100%;
}

.example-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.example-image {
    height: 200px;
    background: var(--secondary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.example-mockup {
    width: 200px;
    height: 150px;
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    padding: 15px;
    display: flex;
    flex-direction: column;
}

.example-mockup .mockup-header {
    height: 20px;
    border-radius: 4px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    padding: 0 8px;
    font-size: 10px;
    font-weight: 600;
    color: var(--white);
}

.bakery .mockup-header { background: #d97706; }
.salon .mockup-header { background: #ec4899; }
.restaurant .mockup-header { background: #dc2626; }
.artisan .mockup-header { background: #059669; }

.example-mockup .mockup-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.example-mockup .mockup-text {
    height: 12px;
    background: var(--text-primary);
    border-radius: 2px;
    font-size: 8px;
    display: flex;
    align-items: center;
    padding: 0 4px;
    color: var(--white);
    font-weight: 500;
}

.mockup-grid, .mockup-list, .mockup-menu, .mockup-portfolio {
    display: grid;
    gap: 4px;
    flex: 1;
}

.mockup-grid {
    grid-template-columns: repeat(3, 1fr);
}

.mockup-list .mockup-item,
.mockup-menu .mockup-item {
    height: 8px;
    background: var(--border-color);
    border-radius: 2px;
}

.mockup-portfolio {
    grid-template-columns: repeat(2, 1fr);
}

.mockup-grid .mockup-item,
.mockup-portfolio .mockup-item {
    aspect-ratio: 1;
    background: var(--border-color);
    border-radius: 2px;
}

.example-content {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.example-content h3 {
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.example-content p {
    margin-bottom: 1rem;
    font-size: 0.9rem;
    flex-grow: 1;
}

.example-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    min-height: 60px;
    align-items: flex-start;
    align-content: flex-start;
}

.feature-tag {
    background: var(--secondary-color);
    color: var(--primary-color);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Benefits Section */
.benefits {
    background: var(--white);
}

.benefits-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: start;
}

.benefit-item {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.benefit-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.benefit-icon i {
    font-size: 1.5rem;
    color: var(--white);
}

.benefit-content h3 {
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.benefit-content p {
    color: var(--text-secondary);
    margin: 0;
}

.benefits-stats {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.stat-card {
    background: var(--gradient);
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    text-align: center;
    color: var(--white);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-text {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Pricing Section */
.pricing {
    background: var(--secondary-color);
}

.pricing-card {
    max-width: 500px;
    margin: 0 auto;
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
}

.pricing-header {
    background: var(--gradient);
    color: var(--white);
    padding: 2rem;
    text-align: center;
}

.pricing-badge {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 1rem;
    display: inline-block;
}

.pricing-price {
    margin-bottom: 1rem;
}

.price-amount {
    font-size: 3rem;
    font-weight: 700;
}

.price-period {
    font-size: 1.2rem;
    opacity: 0.9;
}

.pricing-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

.pricing-features {
    padding: 2rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.feature-item i {
    color: var(--accent-color);
    font-size: 1.2rem;
}

.pricing-cta {
    padding: 0 2rem 2rem;
    text-align: center;
}

.pricing-note {
    margin-top: 1rem;
    font-size: 0.9rem;
    color: var(--text-light);
}

/* Contact Section */
.contact {
    background: var(--white);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-info h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.contact-info p {
    margin-bottom: 2rem;
}

.contact-methods {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.contact-method {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.contact-method i {
    width: 40px;
    height: 40px;
    background: var(--secondary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 1.2rem;
}

.contact-method strong {
    display: block;
    margin-bottom: 0.25rem;
    color: var(--text-primary);
}

.contact-method p {
    margin: 0;
    color: var(--text-secondary);
}

/* Contact Form */
.contact-form {
    background: var(--secondary-color);
    padding: 2rem;
    border-radius: var(--border-radius-lg);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
    background: var(--white);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* Footer */
.footer {
    background: var(--text-primary);
    color: var(--white);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: var(--white);
}

.footer-section p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
}

.footer-section ul li a:hover {
    color: var(--white);
}

.footer-social {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.footer-social a {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    text-decoration: none;
    transition: var(--transition);
}

.footer-social a:hover {
    background: var(--primary-color);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-bottom p {
    margin: 0;
    color: rgba(255, 255, 255, 0.8);
}

.footer-links {
    display: flex;
    gap: 2rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--white);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }

    h1 { font-size: 2.5rem; }
    h2 { font-size: 2rem; }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--white);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow-md);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .hero .container {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-cta {
        justify-content: center;
    }

    .mockup-container {
        height: 300px;
        margin-top: 2rem;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .examples-grid {
        grid-template-columns: 1fr;
    }

    .benefits-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }

    .footer-links {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-cta {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 300px;
    }

    .service-card,
    .contact-form {
        padding: 1.5rem;
    }

    .benefit-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .stat-card {
        padding: 1.5rem;
    }

    .pricing-header,
    .pricing-features,
    .pricing-cta {
        padding: 1.5rem;
    }
}
