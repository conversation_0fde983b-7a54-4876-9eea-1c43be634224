<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Module Commande en Ligne & Click & Collect DigitalVitrine - Solution complète pour métiers de bouche. +15€/mois.">
    <title>Module Commande en Ligne & Click & Collect - DigitalVitrine</title>
    
    <link rel="stylesheet" href="../assets/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="../index.html" class="nav-logo">
                <img src="../assets/images/logo.png" alt="DigitalVitrine Logo">
                <span class="nav-logo-text">DigitalVitrine</span>
            </a>
            <ul class="nav-menu">
                <li class="nav-dropdown">
                    <a href="#modules">Modules <i class="fas fa-chevron-down"></i></a>
                    <div class="nav-dropdown-content">
                        <a href="base.html">Module de Base</a>
                        <a href="commande.html">Commande & Click & Collect</a>
                        <a href="communication.html">Communication & Promotions</a>
                        <a href="google.html">Google My Business</a>
                        <a href="traiteur.html">Spécialisé Traiteur</a>
                        <a href="origine.html">Origine & Préparations</a>
                    </div>
                </li>
                <li><a href="../pricing.html">Tarifs</a></li>
                <li><a href="../index.html#exemples">Exemples</a></li>
                <li><a href="../index.html#contact">Contact</a></li>
            </ul>
            <button class="mobile-menu-btn">
                <i class="fas fa-bars"></i>
            </button>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">
                    Module <span class="highlight">Commande en Ligne & Click & Collect</span>
                </h1>
                <p class="hero-subtitle">
                    Le module essentiel qui transforme votre activité. Vos clients commandent en ligne, 
                    vous préparez à l'avance, tout le monde gagne du temps.
                </p>
                <div class="hero-cta">
                    <a href="../pricing.html" class="btn btn-primary btn-large">Ajouter à +15€/mois</a>
                    <a href="#fonctionnalites" class="btn btn-secondary btn-large">Découvrir les fonctionnalités</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Bénéfices Immédiats -->
    <section class="section">
        <div class="container">
            <div class="section-header">
                <h2>Les Bénéfices Immédiats</h2>
                <p>Pourquoi ce module change tout pour votre activité</p>
            </div>
            
            <div class="benefits-grid">
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-clock" style="color: var(--accent-color);"></i>
                    </div>
                    <h3>Gain de Temps Quotidien</h3>
                    <p>Fini les commandes téléphoniques répétitives. Vos clients commandent en ligne, 
                    vous recevez les détails par email et préparez à l'avance.</p>
                    <div class="benefit-stat">-60% de temps au téléphone</div>
                </div>
                
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-euro-sign" style="color: var(--secondary-color);"></i>
                    </div>
                    <h3>Augmentation du Chiffre d'Affaires</h3>
                    <p>Vos clients peuvent commander même quand vous êtes occupé ou fermé. 
                    Récupérez toutes les ventes perdues par manque de disponibilité.</p>
                    <div class="benefit-stat">+35% de CA en moyenne</div>
                </div>
                
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-heart" style="color: var(--primary-color);"></i>
                    </div>
                    <h3>Satisfaction Client</h3>
                    <p>Plus d'attente, plus de queue. Vos clients récupèrent leurs commandes 
                    au créneau choisi, préparées selon leurs spécifications.</p>
                    <div class="benefit-stat">98% de satisfaction</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Fonctionnalités Détaillées -->
    <section id="fonctionnalites" class="section" style="background: var(--background-light);">
        <div class="container">
            <div class="section-header">
                <h2>Fonctionnalités Complètes</h2>
                <p>Tout ce dont vous avez besoin pour la commande en ligne</p>
            </div>
            
            <!-- Catalogue Produits Complet -->
            <div class="feature-section">
                <div class="feature-content">
                    <div class="feature-text">
                        <h3><i class="fas fa-shopping-cart"></i> Catalogue Produits Complet avec Commande</h3>
                        <p class="feature-description">
                            Transformez votre catalogue consultatif en véritable boutique en ligne 
                            avec toutes les options spécifiques à votre métier.
                        </p>
                        <ul class="feature-list">
                            <li><i class="fas fa-check"></i> Photos haute qualité de tous vos produits</li>
                            <li><i class="fas fa-check"></i> Descriptions détaillées et prix au kg/pièce</li>
                            <li><i class="fas fa-check"></i> Estimation automatique du poids/prix</li>
                            <li><i class="fas fa-check"></i> Catégorisation avancée par type de produit</li>
                            <li><i class="fas fa-check"></i> Mise en avant des produits de saison</li>
                            <li><i class="fas fa-check"></i> Gestion des stocks en temps réel</li>
                            <li><i class="fas fa-check"></i> Alertes "Plus que X disponibles"</li>
                        </ul>
                    </div>
                    <div class="feature-mockup">
                        <div class="mockup-phone">
                            <div class="mockup-screen">
                                <div class="mockup-app-header">Nos Produits</div>
                                <div class="product-catalog">
                                    <div class="product-card">
                                        <div class="product-image-large"></div>
                                        <div class="product-details">
                                            <h4>Côte de Bœuf Maturée</h4>
                                            <p class="product-price">28€/kg</p>
                                            <p class="product-stock">Plus que 3 disponibles</p>
                                            <div class="product-options">
                                                <select class="option-select">
                                                    <option>Épaisseur 2cm</option>
                                                    <option>Épaisseur 3cm</option>
                                                </select>
                                            </div>
                                            <button class="add-to-cart-btn">Ajouter au panier</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Options de Préparation -->
            <div class="feature-section reverse">
                <div class="feature-content">
                    <div class="feature-text">
                        <h3><i class="fas fa-cut"></i> Options de Préparation Spécialisées</h3>
                        <p class="feature-description">
                            Chaque métier a ses spécificités. Le module s'adapte parfaitement 
                            aux besoins de préparation de votre activité.
                        </p>
                        <div class="preparation-types">
                            <div class="prep-type">
                                <h4><i class="fas fa-cut" style="color: var(--butcher-color);"></i> Boucherie</h4>
                                <ul>
                                    <li>Cuisson (bleu, saignant, à point, bien cuit)</li>
                                    <li>Épaisseur de tranche (1cm, 2cm, 3cm...)</li>
                                    <li>Préparation (rôti ficelé, paupiettes, hachage)</li>
                                    <li>Désossage et parage</li>
                                </ul>
                            </div>
                            <div class="prep-type">
                                <h4><i class="fas fa-fish" style="color: var(--fishmonger-color);"></i> Poissonnerie</h4>
                                <ul>
                                    <li>Préparation (vidé, étêté, écaillé)</li>
                                    <li>Découpe (en filets, en darnes, en tronçons)</li>
                                    <li>Plateaux personnalisés</li>
                                    <li>Cuisson sur demande</li>
                                </ul>
                            </div>
                            <div class="prep-type">
                                <h4><i class="fas fa-utensils" style="color: var(--caterer-color);"></i> Traiteur</h4>
                                <ul>
                                    <li>Nombre de parts/personnes</li>
                                    <li>Options accompagnement</li>
                                    <li>Conditionnement (barquette, plat...)</li>
                                    <li>Instructions spéciales</li>
                                </ul>
                            </div>
                            <div class="prep-type">
                                <h4><i class="fas fa-cheese" style="color: var(--cheese-color);"></i> Fromagerie</h4>
                                <ul>
                                    <li>Coupe (pointe, entier, portion)</li>
                                    <li>Niveau d'affinage souhaité</li>
                                    <li>Emballage (papier, sous-vide)</li>
                                    <li>Plateaux composés</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="feature-mockup">
                        <div class="mockup-tablet">
                            <div class="mockup-screen">
                                <div class="options-header">Options de Préparation</div>
                                <div class="options-content">
                                    <div class="option-group">
                                        <label>Épaisseur :</label>
                                        <div class="option-buttons">
                                            <button class="option-btn active">2cm</button>
                                            <button class="option-btn">3cm</button>
                                            <button class="option-btn">4cm</button>
                                        </div>
                                    </div>
                                    <div class="option-group">
                                        <label>Préparation :</label>
                                        <div class="option-buttons">
                                            <button class="option-btn">Nature</button>
                                            <button class="option-btn active">Ficelé</button>
                                        </div>
                                    </div>
                                    <div class="option-group">
                                        <label>Instructions :</label>
                                        <textarea class="option-textarea" placeholder="Demandes spéciales..."></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Click & Collect -->
            <div class="feature-section">
                <div class="feature-content">
                    <div class="feature-text">
                        <h3><i class="fas fa-clock"></i> Créneaux de Retrait Click & Collect</h3>
                        <p class="feature-description">
                            Organisez parfaitement vos retraits avec un système de créneaux 
                            intelligent qui s'adapte à vos horaires et votre charge de travail.
                        </p>
                        <ul class="feature-list">
                            <li><i class="fas fa-check"></i> Créneaux personnalisables selon vos horaires</li>
                            <li><i class="fas fa-check"></i> Limitation du nombre de commandes par créneau</li>
                            <li><i class="fas fa-check"></i> Temps de préparation automatique selon le produit</li>
                            <li><i class="fas fa-check"></i> Gestion des jours fériés et vacances</li>
                            <li><i class="fas fa-check"></i> Notifications SMS/email de rappel</li>
                            <li><i class="fas fa-check"></i> Code de retrait unique par commande</li>
                            <li><i class="fas fa-check"></i> Interface simple pour marquer "retiré"</li>
                        </ul>
                    </div>
                    <div class="feature-mockup">
                        <div class="mockup-browser">
                            <div class="mockup-header">
                                <div class="mockup-buttons">
                                    <span></span><span></span><span></span>
                                </div>
                                <div class="mockup-url">boucherie-martin.fr/retrait</div>
                            </div>
                            <div class="mockup-content">
                                <div class="calendar-header">Choisissez votre créneau de retrait</div>
                                <div class="calendar-grid">
                                    <div class="calendar-day">
                                        <div class="day-header">Aujourd'hui</div>
                                        <div class="time-slots">
                                            <div class="time-slot full">16h-17h (Complet)</div>
                                            <div class="time-slot available">17h-18h (3 places)</div>
                                        </div>
                                    </div>
                                    <div class="calendar-day">
                                        <div class="day-header">Demain</div>
                                        <div class="time-slots">
                                            <div class="time-slot available">9h-10h (5 places)</div>
                                            <div class="time-slot available">10h-11h (4 places)</div>
                                            <div class="time-slot available">16h-17h (5 places)</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Use Cases Détaillés -->
    <section class="section">
        <div class="container">
            <div class="section-header">
                <h2>Cas d'Usage Concrets par Métier</h2>
                <p>Découvrez comment ce module transforme chaque type d'activité</p>
            </div>
            
            <div class="detailed-use-cases">
                <div class="use-case-detailed">
                    <div class="use-case-header">
                        <div class="use-case-icon">
                            <i class="fas fa-cut" style="color: var(--butcher-color);"></i>
                        </div>
                        <h3>Boucherie-Charcuterie</h3>
                    </div>
                    <div class="use-case-scenario">
                        <h4>Scénario :</h4>
                        <p>Un client veut organiser un barbecue pour le week-end. Il a besoin de viande pour 8 personnes 
                        avec des préférences de cuisson différentes.</p>
                    </div>
                    <div class="use-case-process">
                        <h4>Processus avec le module :</h4>
                        <ol>
                            <li><strong>Jeudi soir :</strong> Le client navigue sur le site, sélectionne côtes de bœuf, 
                            merguez, et saucisses. Il précise les épaisseurs et quantités.</li>
                            <li><strong>Vendredi matin :</strong> Vous recevez la commande par email avec tous les détails. 
                            Vous préparez et réservez les produits.</li>
                            <li><strong>Samedi matin :</strong> Le client arrive à son créneau (10h-11h), 
                            présente son code, récupère sa commande en 2 minutes.</li>
                        </ol>
                    </div>
                    <div class="use-case-benefits">
                        <h4>Bénéfices :</h4>
                        <div class="benefit-tags">
                            <span class="benefit-tag">Pas de queue</span>
                            <span class="benefit-tag">Préparation optimisée</span>
                            <span class="benefit-tag">Client satisfait</span>
                            <span class="benefit-tag">Vente sécurisée</span>
                        </div>
                    </div>
                </div>

                <div class="use-case-detailed">
                    <div class="use-case-header">
                        <div class="use-case-icon">
                            <i class="fas fa-fish" style="color: var(--fishmonger-color);"></i>
                        </div>
                        <h3>Poissonnerie</h3>
                    </div>
                    <div class="use-case-scenario">
                        <h4>Scénario :</h4>
                        <p>Une famille veut commander un plateau de fruits de mer pour le réveillon. 
                        Ils ont des préférences précises et veulent être sûrs de la fraîcheur.</p>
                    </div>
                    <div class="use-case-process">
                        <h4>Processus avec le module :</h4>
                        <ol>
                            <li><strong>Une semaine avant :</strong> Commande en ligne du plateau "Réveillon 6 personnes" 
                            avec personnalisation (plus d'huîtres, pas de bulots).</li>
                            <li><strong>Veille du réveillon :</strong> Vous préparez le plateau avec les produits 
                            les plus frais, selon les spécifications.</li>
                            <li><strong>Jour J :</strong> Retrait à 17h, plateau prêt, étiquetage des produits 
                            avec conseils de dégustation.</li>
                        </ol>
                    </div>
                    <div class="use-case-benefits">
                        <h4>Bénéfices :</h4>
                        <div class="benefit-tags">
                            <span class="benefit-tag">Commande anticipée</span>
                            <span class="benefit-tag">Personnalisation</span>
                            <span class="benefit-tag">Fraîcheur garantie</span>
                            <span class="benefit-tag">Conseil expert</span>
                        </div>
                    </div>
                </div>

                <div class="use-case-detailed">
                    <div class="use-case-header">
                        <div class="use-case-icon">
                            <i class="fas fa-utensils" style="color: var(--caterer-color);"></i>
                        </div>
                        <h3>Traiteur</h3>
                    </div>
                    <div class="use-case-scenario">
                        <h4>Scénario :</h4>
                        <p>Un bureau veut commander des plateaux repas pour une réunion de 15 personnes. 
                        Ils ont des contraintes alimentaires à respecter.</p>
                    </div>
                    <div class="use-case-process">
                        <h4>Processus avec le module :</h4>
                        <ol>
                            <li><strong>3 jours avant :</strong> Commande de 15 plateaux repas avec options 
                            (5 végétariens, 2 sans gluten, 8 classiques).</li>
                            <li><strong>Veille :</strong> Préparation selon les spécifications, 
                            étiquetage des plateaux par régime alimentaire.</li>
                            <li><strong>Jour J :</strong> Livraison ou retrait à 11h30, 
                            plateaux prêts avec couverts et serviettes.</li>
                        </ol>
                    </div>
                    <div class="use-case-benefits">
                        <h4>Bénéfices :</h4>
                        <div class="benefit-tags">
                            <span class="benefit-tag">Gestion contraintes</span>
                            <span class="benefit-tag">Commande groupée</span>
                            <span class="benefit-tag">Livraison possible</span>
                            <span class="benefit-tag">Client professionnel</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing CTA -->
    <section class="section" style="background: var(--background-light);">
        <div class="container">
            <div class="cta-section">
                <h2>Ajoutez le Module Commande & Click & Collect</h2>
                <p>Transformez votre activité avec la commande en ligne</p>
                <div class="cta-price">
                    <span class="price-large">+15€</span>
                    <span class="price-period">/mois</span>
                </div>
                <p class="cta-note">En complément du Module de Base • Formation incluse • Support spécialisé</p>
                <div class="cta-buttons">
                    <a href="../pricing.html" class="btn btn-primary btn-large">Voir tous les Tarifs</a>
                    <a href="../index.html#contact" class="btn btn-secondary btn-large">Demander une Démo</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>DigitalVitrine</h3>
                    <p>Solutions modulaires pour métiers de bouche.</p>
                </div>
                <div class="footer-section">
                    <h4>Modules</h4>
                    <ul>
                        <li><a href="base.html">Module de Base</a></li>
                        <li><a href="commande.html">Commande en ligne</a></li>
                        <li><a href="communication.html">Communication</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Support</h4>
                    <ul>
                        <li><a href="#">Documentation</a></li>
                        <li><a href="#">Formation</a></li>
                        <li><a href="#">Contact</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 DigitalVitrine. Tous droits réservés.</p>
            </div>
        </div>
    </footer>

    <script src="../assets/script.js"></script>
</body>
</html>
