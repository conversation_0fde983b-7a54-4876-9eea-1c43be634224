/* DigitalVitrine - Styles communs pour tous les modules */

:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #f59e0b;
    --accent-color: #10b981;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --background: #ffffff;
    --background-light: #f9fafb;
    --background-dark: #f3f4f6;
    --border-color: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    --border-radius: 8px;
    --white: #ffffff;
    
    /* Couleurs spécifiques métiers de bouche */
    --butcher-color: #dc2626;
    --fishmonger-color: #0ea5e9;
    --caterer-color: #059669;
    --cheese-color: #f59e0b;
    --bakery-color: #d97706;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background);
}

/* Navigation */
.navbar {
    background: var(--white);
    box-shadow: var(--shadow-sm);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    padding: 1rem 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.nav-logo img {
    height: 40px;
    width: auto;
}

.nav-logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
    align-items: center;
}

.nav-menu a {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-menu a:hover {
    color: var(--primary-color);
}

.nav-dropdown {
    position: relative;
}

.nav-dropdown-content {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background: var(--white);
    box-shadow: var(--shadow-lg);
    border-radius: var(--border-radius);
    padding: 1rem 0;
    min-width: 250px;
    z-index: 1001;
}

.nav-dropdown:hover .nav-dropdown-content {
    display: block;
}

.nav-dropdown-content a {
    display: block;
    padding: 0.5rem 1rem;
    color: var(--text-primary);
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.nav-dropdown-content a:hover {
    background-color: var(--background-light);
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Sections */
.section {
    padding: 5rem 0;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-header h1,
.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.section-header p {
    font-size: 1.25rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
    padding: 8rem 0 6rem;
    margin-top: 80px;
}

.hero-content {
    text-align: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.highlight {
    color: var(--secondary-color);
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 1rem 2rem;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 600;
    text-align: center;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary {
    background: var(--secondary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: #d97706;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: transparent;
    color: var(--white);
    border: 2px solid var(--white);
}

.btn-secondary:hover {
    background: var(--white);
    color: var(--primary-color);
}

.btn-large {
    padding: 1.25rem 2.5rem;
    font-size: 1.125rem;
}

/* Cards */
.card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    padding: 2rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

/* Module Cards */
.modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.module-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.module-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.module-header {
    padding: 2rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
    text-align: center;
}

.module-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.module-subtitle {
    opacity: 0.9;
    font-size: 1rem;
}

.module-content {
    padding: 2rem;
}

.module-features {
    list-style: none;
    margin-bottom: 2rem;
}

.module-features li {
    padding: 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.module-features i {
    color: var(--accent-color);
    width: 20px;
}

.module-price {
    text-align: center;
    padding: 1.5rem;
    background: var(--background-light);
    border-top: 1px solid var(--border-color);
}

.price-amount {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.price-period {
    color: var(--text-secondary);
    font-size: 1rem;
}

/* Responsive */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.25rem;
    }
    
    .modules-grid {
        grid-template-columns: 1fr;
    }
    
    .container {
        padding: 0 1rem;
    }
}

/* Footer */
.footer {
    background: var(--text-primary);
    color: var(--white);
    padding: 3rem 0 2rem;
    margin-top: 5rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: var(--white);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #d1d5db;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: var(--white);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #374151;
    color: #9ca3af;
}

/* Feature Sections */
.feature-section {
    margin: 4rem 0;
    padding: 3rem 0;
}

.feature-section.reverse .feature-content {
    flex-direction: row-reverse;
}

.feature-content {
    display: flex;
    align-items: center;
    gap: 4rem;
}

.feature-text {
    flex: 1;
}

.feature-text h3 {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.feature-text h3 i {
    color: var(--primary-color);
    margin-right: 0.5rem;
}

.feature-description {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    line-height: 1.7;
}

.feature-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.feature-list li {
    padding: 0.5rem 0;
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.feature-list i {
    color: var(--accent-color);
    margin-top: 0.25rem;
    flex-shrink: 0;
}

.feature-mockup {
    flex: 1;
    display: flex;
    justify-content: center;
}

/* Mockups */
.mockup-browser {
    width: 400px;
    background: var(--white);
    border-radius: 8px;
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

.mockup-header {
    background: #f3f4f6;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.mockup-buttons {
    display: flex;
    gap: 0.5rem;
}

.mockup-buttons span {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #ef4444;
}

.mockup-buttons span:nth-child(2) {
    background: #f59e0b;
}

.mockup-buttons span:nth-child(3) {
    background: #10b981;
}

.mockup-url {
    background: var(--white);
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-size: 0.875rem;
    color: var(--text-secondary);
    flex: 1;
}

.mockup-content {
    padding: 2rem;
}

.mockup-nav {
    font-weight: 700;
    font-size: 1.25rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.mockup-hero h4 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.mockup-hero p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.mockup-sections {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.mockup-section {
    background: var(--background-light);
    padding: 1rem;
    border-radius: 4px;
    text-align: center;
    font-weight: 500;
    color: var(--text-primary);
}

/* Phone Mockup */
.mockup-phone {
    width: 250px;
    height: 500px;
    background: #1f2937;
    border-radius: 25px;
    padding: 20px;
    box-shadow: var(--shadow-lg);
}

.mockup-screen {
    width: 100%;
    height: 100%;
    background: var(--white);
    border-radius: 15px;
    padding: 1rem;
    overflow: hidden;
}

.mockup-app-header {
    font-weight: 700;
    font-size: 1.125rem;
    color: var(--text-primary);
    margin-bottom: 1rem;
    text-align: center;
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.product-item {
    background: var(--background-light);
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
}

.product-image {
    width: 60px;
    height: 60px;
    background: var(--border-color);
    border-radius: 8px;
    margin: 0 auto 0.5rem;
}

.product-info h5 {
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: var(--text-primary);
}

.product-info p {
    font-size: 0.75rem;
    color: var(--primary-color);
    font-weight: 600;
}

/* Tablet Mockup */
.mockup-tablet {
    width: 350px;
    height: 250px;
    background: #1f2937;
    border-radius: 15px;
    padding: 15px;
    box-shadow: var(--shadow-lg);
}

.ardoise-header {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
    text-align: center;
}

.ardoise-content {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.ardoise-item {
    background: var(--background-light);
    padding: 0.75rem;
    border-radius: 6px;
    border-left: 4px solid var(--primary-color);
}

.ardoise-item h4 {
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: var(--text-primary);
}

.ardoise-item p {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin: 0;
}

/* Use Cases */
.use-cases-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.use-case-card {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow-md);
    transition: transform 0.3s ease;
}

.use-case-card:hover {
    transform: translateY(-5px);
}

.use-case-icon {
    width: 60px;
    height: 60px;
    background: var(--background-light);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.use-case-icon i {
    font-size: 1.5rem;
}

.use-case-card h3 {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.use-case-scenario,
.use-case-solution {
    margin-bottom: 1rem;
    line-height: 1.6;
}

.use-case-scenario strong,
.use-case-solution strong {
    color: var(--primary-color);
}

.use-case-benefits {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-top: 1.5rem;
}

.benefit-tag {
    background: var(--accent-color);
    color: var(--white);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
}

/* CTA Section */
.cta-section {
    text-align: center;
    background: var(--white);
    padding: 4rem 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    margin: 3rem 0;
}

.cta-section h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.cta-section p {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.cta-price {
    margin: 2rem 0;
}

.price-large {
    font-size: 4rem;
    font-weight: 700;
    color: var(--primary-color);
}

.cta-price .price-period {
    font-size: 1.5rem;
    color: var(--text-secondary);
    margin-left: 0.5rem;
}

.cta-note {
    font-size: 1rem;
    color: var(--text-light);
    margin-bottom: 2rem;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Responsive */
@media (max-width: 768px) {
    .feature-content {
        flex-direction: column;
        gap: 2rem;
    }

    .feature-section.reverse .feature-content {
        flex-direction: column;
    }

    .mockup-browser {
        width: 100%;
        max-width: 350px;
    }

    .mockup-phone {
        width: 200px;
        height: 400px;
    }

    .mockup-tablet {
        width: 280px;
        height: 200px;
    }

    .use-cases-grid {
        grid-template-columns: 1fr;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .price-large {
        font-size: 3rem;
    }
}

/* Pricing Page Styles */
.pricing-base {
    display: flex;
    justify-content: center;
    margin: 3rem 0;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.packs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.pricing-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    position: relative;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.pricing-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.pricing-card.featured {
    border: 2px solid var(--primary-color);
    transform: scale(1.05);
}

.pricing-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--secondary-color);
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    z-index: 1;
}

.pricing-badge.popular {
    background: var(--accent-color);
}

.pricing-header {
    padding: 2rem;
    text-align: center;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
}

.pricing-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.pricing-subtitle {
    opacity: 0.9;
    margin-bottom: 1.5rem;
}

.pricing-price {
    margin: 1rem 0;
}

.pricing-features {
    padding: 2rem;
}

.pricing-features h4 {
    color: var(--primary-color);
    font-size: 1.125rem;
    font-weight: 600;
    margin: 1.5rem 0 1rem 0;
    border-bottom: 2px solid var(--background-light);
    padding-bottom: 0.5rem;
}

.pricing-features h4:first-child {
    margin-top: 0;
}

.pricing-features ul {
    list-style: none;
    margin: 0 0 1rem 0;
    padding: 0;
}

.pricing-features li {
    padding: 0.5rem 0;
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.pricing-features i {
    color: var(--accent-color);
    margin-top: 0.25rem;
    flex-shrink: 0;
}

.use-case {
    background: var(--background-light);
    padding: 1rem;
    border-radius: 6px;
    margin-top: 1rem;
    font-style: italic;
    border-left: 4px solid var(--primary-color);
}

.pricing-cta {
    padding: 1.5rem 2rem;
    text-align: center;
    background: var(--background-light);
    border-top: 1px solid var(--border-color);
}

.price-save {
    font-size: 0.875rem;
    color: var(--accent-color);
    font-weight: 600;
    margin-top: 0.5rem;
}

/* Price Calculator */
.price-calculator {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    padding: 3rem;
    margin: 3rem 0;
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    align-items: start;
}

.calculator-modules {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.module-option {
    display: flex;
    align-items: center;
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.module-option:hover {
    border-color: var(--primary-color);
    background: var(--background-light);
}

.module-option input[type="checkbox"] {
    margin-right: 1rem;
    transform: scale(1.2);
}

.module-option input[type="checkbox"]:disabled {
    opacity: 0.5;
}

.module-option label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    cursor: pointer;
    margin: 0;
}

.module-name {
    font-weight: 500;
    color: var(--text-primary);
}

.module-option .module-price {
    font-weight: 600;
    color: var(--primary-color);
}

.calculator-result {
    background: var(--background-light);
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    position: sticky;
    top: 2rem;
}

.total-price {
    margin-bottom: 2rem;
}

.price-label {
    display: block;
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

#total-price {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

/* Contact Grid */
.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    margin-top: 3rem;
}

.contact-info h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--white);
}

.contact-methods {
    margin-top: 2rem;
}

.contact-method {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    color: rgba(255,255,255,0.9);
}

.contact-method i {
    width: 20px;
    color: var(--secondary-color);
}

.contact-form {
    background: var(--white);
    padding: 2rem;
    border-radius: var(--border-radius);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* Responsive Pricing */
@media (max-width: 768px) {
    .pricing-grid {
        grid-template-columns: 1fr;
    }

    .packs-grid {
        grid-template-columns: 1fr;
    }

    .price-calculator {
        grid-template-columns: 1fr;
        gap: 2rem;
        padding: 2rem;
    }

    .contact-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .pricing-card.featured {
        transform: none;
    }
}

/* Module Pages Specific Styles */
.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.benefit-card {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: transform 0.3s ease;
}

.benefit-card:hover {
    transform: translateY(-5px);
}

.benefit-icon {
    width: 80px;
    height: 80px;
    background: var(--background-light);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.benefit-icon i {
    font-size: 2rem;
}

.benefit-card h3 {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.benefit-stat {
    background: var(--primary-color);
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.875rem;
    margin-top: 1rem;
    display: inline-block;
}

/* Product Catalog Mockup */
.product-catalog {
    padding: 1rem 0;
}

.product-card {
    background: var(--background-light);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.product-image-large {
    width: 100%;
    height: 80px;
    background: var(--border-color);
    border-radius: 6px;
    margin-bottom: 0.75rem;
}

.product-details h4 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.product-price {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.product-stock {
    font-size: 0.75rem;
    color: #ef4444;
    margin-bottom: 0.75rem;
}

.product-options {
    margin-bottom: 0.75rem;
}

.option-select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 0.875rem;
}

.add-to-cart-btn {
    width: 100%;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: 0.75rem;
    border-radius: 6px;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
}

/* Preparation Types */
.preparation-types {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.prep-type {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    border-left: 4px solid var(--primary-color);
}

.prep-type h4 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.prep-type ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.prep-type li {
    padding: 0.25rem 0;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.prep-type li:before {
    content: "•";
    color: var(--primary-color);
    margin-right: 0.5rem;
}

/* Options Mockup */
.options-header {
    font-weight: 700;
    font-size: 1.125rem;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    text-align: center;
}

.options-content {
    padding: 1rem;
}

.option-group {
    margin-bottom: 1.5rem;
}

.option-group label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.option-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.option-btn {
    padding: 0.5rem 1rem;
    border: 2px solid var(--border-color);
    background: var(--white);
    border-radius: 20px;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.option-btn.active {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: var(--white);
}

.option-textarea {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 0.75rem;
    resize: none;
    height: 60px;
}

/* Calendar Mockup */
.calendar-header {
    font-weight: 700;
    font-size: 1.125rem;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    text-align: center;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.calendar-day {
    background: var(--background-light);
    border-radius: 6px;
    padding: 1rem;
}

.day-header {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    text-align: center;
    font-size: 0.875rem;
}

.time-slots {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.time-slot {
    padding: 0.5rem;
    border-radius: 4px;
    text-align: center;
    font-size: 0.75rem;
    font-weight: 500;
}

.time-slot.available {
    background: var(--accent-color);
    color: var(--white);
    cursor: pointer;
}

.time-slot.full {
    background: #ef4444;
    color: var(--white);
    opacity: 0.7;
}

/* Detailed Use Cases */
.detailed-use-cases {
    display: flex;
    flex-direction: column;
    gap: 3rem;
    margin-top: 3rem;
}

.use-case-detailed {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.use-case-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.use-case-header .use-case-icon {
    width: 60px;
    height: 60px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0;
}

.use-case-header .use-case-icon i {
    font-size: 1.5rem;
    color: var(--white);
}

.use-case-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.use-case-scenario,
.use-case-process,
.use-case-benefits {
    padding: 2rem;
    border-bottom: 1px solid var(--border-color);
}

.use-case-detailed .use-case-benefits {
    border-bottom: none;
}

.use-case-scenario h4,
.use-case-process h4,
.use-case-benefits h4 {
    color: var(--primary-color);
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.use-case-process ol {
    margin: 0;
    padding-left: 1.5rem;
}

.use-case-process li {
    margin-bottom: 1rem;
    line-height: 1.6;
}

.use-case-process strong {
    color: var(--primary-color);
}

.benefit-tags {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

/* Responsive Module Pages */
@media (max-width: 768px) {
    .benefits-grid {
        grid-template-columns: 1fr;
    }

    .preparation-types {
        grid-template-columns: 1fr;
    }

    .calendar-grid {
        grid-template-columns: 1fr;
    }

    .use-case-header {
        flex-direction: column;
        text-align: center;
    }

    .detailed-use-cases {
        gap: 2rem;
    }
}
