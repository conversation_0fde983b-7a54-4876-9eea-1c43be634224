# DigitalVitrine - Site Web Vitrine

Site web professionnel pour DigitalVitrine, spécialisé dans la création de sites web vitrine pour commerçants et artisans français.

## 🚀 Déploiement sur GitHub Pages

### Étapes de déploiement :

1. **Créer un repository GitHub**
   ```bash
   git init
   git add .
   git commit -m "Initial commit - DigitalVitrine website"
   git branch -M main
   git remote add origin https://github.com/VOTRE-USERNAME/digitalvitrine.git
   git push -u origin main
   ```

2. **Activer GitHub Pages**
   - Allez dans les paramètres de votre repository
   - Scrollez jusqu'à la section "Pages"
   - Sélectionnez "Deploy from a branch"
   - Choi<PERSON><PERSON>z "main" comme branche source
   - Sélectionnez "/ (root)" comme dossier
   - Cliquez sur "Save"

3. **Accéder au site**
   - Votre site sera disponible à l'adresse : `https://VOTRE-USERNAME.github.io/digitalvitrine/`
   - Le déploiement peut prendre quelques minutes

### Configuration du domaine personnalisé (optionnel)

Si vous souhaitez utiliser un domaine personnalisé comme `digitalvitrine.fr` :

1. Créez un fichier `CNAME` à la racine du projet avec votre domaine :
   ```
   digitalvitrine.fr
   ```

2. Configurez les DNS de votre domaine pour pointer vers GitHub Pages :
   ```
   Type: CNAME
   Name: www
   Value: VOTRE-USERNAME.github.io
   
   Type: A
   Name: @
   Value: ***************
   Value: ***************
   Value: ***************
   Value: ***************
   ```

## 📁 Structure du projet

```
digitalvitrine/
├── index.html          # Page principale
├── styles.css          # Styles CSS
├── script.js           # JavaScript interactif
├── README.md           # Documentation
└── images/             # Dossier pour les images (à créer si nécessaire)
```

## 🎨 Fonctionnalités

### Design
- **Responsive** : Adapté à tous les écrans (mobile, tablette, desktop)
- **Moderne** : Design professionnel avec animations fluides
- **Accessible** : Navigation claire et intuitive

### Sections
- **Hero** : Proposition de valeur principale
- **Services** : Présentation des services offerts
- **Exemples** : Mockups de différents types de commerces
- **Avantages** : Bénéfices pour les commerçants
- **Tarifs** : Offre unique à 10€/mois
- **Contact** : Formulaire de contact avec validation

### Interactivité
- Navigation mobile avec menu hamburger
- Scroll fluide entre les sections
- Animations au scroll
- Validation de formulaire en temps réel
- Messages de succès/erreur

## 🛠️ Personnalisation

### Couleurs
Les couleurs principales sont définies dans les variables CSS (`styles.css`) :
```css
:root {
    --primary-color: #2563eb;    /* Bleu principal */
    --accent-color: #10b981;     /* Vert accent */
    --text-primary: #1f2937;     /* Texte principal */
    /* ... */
}
```

### Contenu
- Modifiez le contenu directement dans `index.html`
- Ajustez les informations de contact
- Personnalisez les exemples de réalisations

### Images
- Ajoutez vos propres images dans le dossier `images/`
- Remplacez les mockups par de vraies captures d'écran
- Optimisez les images pour le web (format WebP recommandé)

## 📧 Configuration du formulaire de contact

Le formulaire est actuellement configuré pour une démonstration. Pour le rendre fonctionnel :

### Option 1 : Formspree (Recommandé)
1. Créez un compte sur [Formspree.io](https://formspree.io)
2. Modifiez la balise `<form>` dans `index.html` :
   ```html
   <form action="https://formspree.io/f/VOTRE-ID" method="POST" class="contact-form" id="contactForm">
   ```

### Option 2 : Netlify Forms
1. Déployez sur Netlify au lieu de GitHub Pages
2. Ajoutez `netlify` à la balise form :
   ```html
   <form netlify class="contact-form" id="contactForm">
   ```

### Option 3 : Service personnalisé
Intégrez avec votre propre backend ou service de formulaire.

## 🔧 Maintenance

### Mises à jour
- Modifiez les fichiers localement
- Committez et poussez les changements :
  ```bash
  git add .
  git commit -m "Description des modifications"
  git push
  ```
- GitHub Pages se met à jour automatiquement

### Monitoring
- Utilisez Google Analytics pour suivre le trafic
- Configurez Google Search Console pour le SEO
- Surveillez les performances avec PageSpeed Insights

## 📱 Compatibilité

- ✅ Chrome, Firefox, Safari, Edge (versions récentes)
- ✅ iOS Safari, Chrome Mobile
- ✅ Responsive design (320px à 1920px+)
- ✅ Optimisé pour les performances

## 🎯 SEO

Le site est optimisé pour le référencement :
- Meta tags appropriés
- Structure HTML sémantique
- Vitesse de chargement optimisée
- Contenu en français ciblé

## 📞 Support

Pour toute question ou personnalisation :
- Modifiez les informations de contact dans le footer
- Adaptez le contenu selon vos besoins
- Testez régulièrement sur différents appareils

---

**Note** : Ce site est conçu pour être simple à maintenir et à déployer. Aucune connaissance technique avancée n'est requise pour les modifications de contenu de base.
